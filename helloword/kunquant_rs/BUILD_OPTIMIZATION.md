# Build.rs 优化说明

## 🎯 优化目标

优化 `build.rs` 文件，使其能够：
1. **自动搜索** KunRuntime 库文件，而不是写死路径
2. **智能定位** 库文件在不同构建环境中的位置
3. **友好提示** 当库文件未找到时提供详细的解决方案

## 🔍 搜索策略

### 自动搜索逻辑

新的 `build.rs` 采用递归搜索策略，支持以下库文件名：

```rust
let library_patterns = vec![
    "libKunRuntime.so",      // Linux 标准命名
    "KunRuntime.so",         // Linux 简化命名
    "libKunRuntime.dylib",   // macOS 标准命名
    "KunRuntime.dylib",      // macOS 简化命名
    "KunRuntime.dll",        // Windows 命名
    "libKunRuntime.dll",     // Windows 标准命名
];
```

### 搜索路径

1. **主要搜索路径**: `KunQuant/` 目录及其所有子目录
2. **备用搜索路径**: 虚拟环境目录
   - `kunquant-env/`
   - `venv/`
   - `.venv/`

## 🚀 主要功能

### 1. 递归文件搜索

```rust
fn find_files_recursive(dir: &Path, pattern: &str) -> Vec<PathBuf>
```

- 递归遍历指定目录的所有子目录
- 按文件名模式匹配库文件
- 返回所有匹配文件的完整路径

### 2. 库目录定位

```rust
fn find_kunruntime_lib_dirs(search_root: &Path) -> Vec<PathBuf>
```

- 搜索所有可能的库文件名
- 提取库文件的父目录路径
- 去重并返回唯一的库目录列表

### 3. 智能错误处理

当库文件未找到时，提供详细的解决方案：

```
❌ KunRuntime library not found!

🔍 Searched in: /path/to/KunQuant

💡 Possible solutions:
   1. Build KunQuant from source:
      cd KunQuant
      python setup.py build_ext --inplace

   2. Install KunQuant via pip:
      pip install -e ./KunQuant

   3. Build using CMake:
      cd KunQuant
      mkdir build && cd build
      cmake .. && make

   4. Check if KunQuant directory exists:
      ls -la KunQuant/

📋 Expected library files:
   - libKunRuntime.so (Linux)
   - libKunRuntime.dylib (macOS)
   - KunRuntime.dll (Windows)
```

## 📁 支持的目录结构

新的 `build.rs` 支持以下目录结构：

```
KunQuant-rs/
├── KunQuant/
│   ├── build/
│   │   └── lib.linux-x86_64-cpython-312/
│   │       └── KunQuant/
│   │           └── runner/
│   │               └── libKunRuntime.so ✅
│   └── KunQuant/
│       └── runner/
│           └── libKunRuntime.so ✅
├── kunquant-env/
│   └── lib/python3.12/site-packages/
│       └── KunQuant/
│           └── runner/
│               └── libKunRuntime.so ✅
└── build.rs
```

## 🔧 使用方法

### 正常构建

```bash
cargo build
```

如果 KunRuntime 库存在，构建将自动成功。

### 调试构建信息

```bash
cargo build --verbose
```

查看详细的构建过程和库搜索结果。

### 清理重建

```bash
cargo clean && cargo build
```

强制重新运行 `build.rs` 脚本。

## ✅ 优化效果

### 之前的问题
- ❌ 硬编码库路径，不够灵活
- ❌ 不支持不同的构建环境
- ❌ 错误信息不够详细

### 优化后的优势
- ✅ 自动搜索，无需手动配置路径
- ✅ 支持多种构建环境和目录结构
- ✅ 跨平台兼容（Linux、macOS、Windows）
- ✅ 详细的错误提示和解决方案
- ✅ 智能的虚拟环境检测

## 🎉 总结

优化后的 `build.rs` 提供了：
1. **自动化**: 无需手动配置库路径
2. **智能化**: 自动适应不同的构建环境
3. **友好化**: 提供详细的错误信息和解决方案
4. **跨平台**: 支持 Linux、macOS 和 Windows

这使得 KunQuant-rs 的构建过程更加稳定和用户友好！
