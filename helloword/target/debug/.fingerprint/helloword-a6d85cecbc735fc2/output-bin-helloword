{"$message_type":"diagnostic","message":"linking with `cc` failed: exit status: 1","code":null,"level":"error","spans":[],"children":[{"message":" \"cc\" \"-m64\" \"/tmp/rustc6CRKtj/symbols.o\" \"<48 object files omitted>\" \"-Wl,--as-needed\" \"-Wl,-Bstatic\" \"/home/<USER>/projects/helloword/target/debug/deps/{libkunquant_rs-b81200bb3f51c47b.rlib,liblibc-9c9836c48cf0592d.rlib,libthiserror-4c8361a3d054ae28.rlib}.rlib\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-gnu/lib/{libstd-*,libpanic_unwind-*,libobject-*,libmemchr-*,libaddr2line-*,libgimli-*,librustc_demangle-*,libstd_detect-*,libhashbrown-*,librustc_std_workspace_alloc-*,libminiz_oxide-*,libadler2-*,libunwind-*,libcfg_if-*,liblibc-*,liballoc-*,librustc_std_workspace_core-*,libcore-*,libcompiler_builtins-*}.rlib\" \"-Wl,-Bdynamic\" \"-lKunRuntime\" \"-lgcc_s\" \"-lutil\" \"-lrt\" \"-lpthread\" \"-lm\" \"-ldl\" \"-lc\" \"-L\" \"/tmp/rustc6CRKtj/raw-dylibs\" \"-Wl,--eh-frame-hdr\" \"-Wl,-z,noexecstack\" \"-L\" \"/home/<USER>/projects/helloword/kunquant_rs/KunQuant/build\" \"-L\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-gnu/lib\" \"-o\" \"/home/<USER>/projects/helloword/target/debug/deps/helloword-a6d85cecbc735fc2\" \"-Wl,--gc-sections\" \"-pie\" \"-Wl,-z,relro,-z,now\" \"-nodefaultlibs\"","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"some arguments are omitted. use `--verbose` to show all linker arguments","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"/usr/bin/ld: cannot find -lKunRuntime: No such file or directory\ncollect2: error: ld returned 1 exit status\n","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: linking with `cc` failed: exit status: 1\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m:  \"cc\" \"-m64\" \"/tmp/rustc6CRKtj/symbols.o\" \"<48 object files omitted>\" \"-Wl,--as-needed\" \"-Wl,-Bstatic\" \"/home/<USER>/projects/helloword/target/debug/deps/{libkunquant_rs-b81200bb3f51c47b.rlib,liblibc-9c9836c48cf0592d.rlib,libthiserror-4c8361a3d054ae28.rlib}.rlib\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-gnu/lib/{libstd-*,libpanic_unwind-*,libobject-*,libmemchr-*,libaddr2line-*,libgimli-*,librustc_demangle-*,libstd_detect-*,libhashbrown-*,librustc_std_workspace_alloc-*,libminiz_oxide-*,libadler2-*,libunwind-*,libcfg_if-*,liblibc-*,liballoc-*,librustc_std_workspace_core-*,libcore-*,libcompiler_builtins-*}.rlib\" \"-Wl,-Bdynamic\" \"-lKunRuntime\" \"-lgcc_s\" \"-lutil\" \"-lrt\" \"-lpthread\" \"-lm\" \"-ldl\" \"-lc\" \"-L\" \"/tmp/rustc6CRKtj/raw-dylibs\" \"-Wl,--eh-frame-hdr\" \"-Wl,-z,noexecstack\" \"-L\" \"/home/<USER>/projects/helloword/kunquant_rs/KunQuant/build\" \"-L\" \"<sysroot>/lib/rustlib/x86_64-unknown-linux-gnu/lib\" \"-o\" \"/home/<USER>/projects/helloword/target/debug/deps/helloword-a6d85cecbc735fc2\" \"-Wl,--gc-sections\" \"-pie\" \"-Wl,-z,relro,-z,now\" \"-nodefaultlibs\"\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: some arguments are omitted. use `--verbose` to show all linker arguments\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: /usr/bin/ld: cannot find -lKunRuntime: No such file or directory\u001b[0m\n\u001b[0m          collect2: error: ld returned 1 exit status\u001b[0m\n\u001b[0m          \u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 1 previous error\u001b[0m\n\n"}
