use kunquant_rs::{<PERSON>chPara<PERSON>, BufferNameMap, Executor, Library, Result, run_graph};

fn main() -> Result<()> {
    // Create executor and load library
    let executor = Executor::single_thread()?;
    let library = Library::load("path/to/factor_library.so")?;
    let module = library.get_module("my_module")?;

    // Set up input/output buffers
    let mut buffers = BufferNameMap::new()?;
    let mut input_data = vec![1.0f32; 8 * 100]; // 8 stocks, 100 time points
    let mut output_data = vec![0.0f32; 8 * 100];

    buffers.set_buffer_slice("input", &mut input_data)?;
    buffers.set_buffer_slice("output", &mut output_data)?;

    // Run computation
    let params = BatchParams::full_range(8, 100)?;
    run_graph(&executor, &module, &buffers, &params)?;

    Ok(())
}
